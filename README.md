# Vite Micro Frontend Demo

This project demonstrates a micro frontend architecture using Vite, React, and Module Federation. It consists of two applications:

- **Host App** (port 5000): The main application that consumes remote components
- **Remote App** (port 5001): A micro frontend that exposes components via Module Federation

## Architecture

```
┌─────────────────┐    Module Federation    ┌─────────────────┐
│   Host App      │◄──────────────────────►│   Remote App    │
│   (port 5000)   │                        │   (port 5001)   │
│                 │                        │                 │
│ - Consumes      │                        │ - Exposes       │
│   remote        │                        │   Button        │
│   components    │                        │   Counter       │
└─────────────────┘                        └─────────────────┘
```

## Features

- **Module Federation**: Components are shared between applications at runtime
- **Independent Development**: Each app can be developed and deployed independently
- **Hot Module Replacement**: Both apps support HMR for fast development
- **Shared Dependencies**: React and React-DOM are shared between applications
- **Lazy Loading**: Remote components are loaded on-demand with Suspense

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm

### Installation

1. Install dependencies for all applications:
```bash
npm run install:all
```

### Development

1. Start both applications in development mode:
```bash
npm run dev
```

This will start:
- Remote app on http://localhost:5001
- Host app on http://localhost:5000

2. Or start them individually:
```bash
# Start remote app only
npm run dev:remote

# Start host app only (requires remote app to be running)
npm run dev:host
```

### Building for Production

1. Build both applications:
```bash
npm run build
```

2. Preview the built applications:
```bash
npm run preview
```

## Project Structure

```
vite-micro-apps/
├── host-app/                 # Host application
│   ├── src/
│   │   ├── App.jsx          # Main app with remote component imports
│   │   └── main.jsx
│   ├── vite.config.js       # Vite config with federation setup
│   └── package.json
├── remote-app/               # Remote micro frontend
│   ├── src/
│   │   ├── components/
│   │   │   ├── Button.jsx   # Exposed button component
│   │   │   ├── Button.css
│   │   │   ├── Counter.jsx  # Exposed counter component
│   │   │   └── Counter.css
│   │   ├── App.jsx          # Standalone app showcasing components
│   │   └── main.jsx
│   ├── vite.config.js       # Vite config with federation setup
│   └── package.json
├── package.json              # Root package.json with scripts
└── README.md
```

## Module Federation Configuration

### Remote App (Exposes Components)
```javascript
federation({
  name: 'remote-app',
  filename: 'remoteEntry.js',
  exposes: {
    './Button': './src/components/Button',
    './Counter': './src/components/Counter',
  },
  shared: ['react', 'react-dom']
})
```

### Host App (Consumes Components)
```javascript
federation({
  name: 'host-app',
  remotes: {
    remoteApp: 'http://localhost:5001/assets/remoteEntry.js',
  },
  shared: ['react', 'react-dom']
})
```

## Usage

### In the Host App
```jsx
import React, { Suspense } from 'react'

// Lazy load remote components
const RemoteButton = React.lazy(() => import('remoteApp/Button'))
const RemoteCounter = React.lazy(() => import('remoteApp/Counter'))

function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <RemoteButton onClick={() => console.log('Clicked!')}>
        Click me!
      </RemoteButton>
      <RemoteCounter initialValue={0} step={1} />
    </Suspense>
  )
}
```

## Key Benefits

1. **Independent Deployment**: Each micro frontend can be deployed separately
2. **Technology Agnostic**: Different teams can use different versions of libraries
3. **Runtime Integration**: Components are loaded at runtime, not build time
4. **Shared Dependencies**: Common libraries are shared to reduce bundle size
5. **Development Independence**: Teams can work on different parts simultaneously

## Troubleshooting

- **CORS Issues**: Make sure both applications are running on the specified ports
- **Module Not Found**: Ensure the remote app is running before starting the host app
- **Build Issues**: Check that all dependencies are installed with `npm run install:all`

## Next Steps

- Add error boundaries for better error handling
- Implement routing with micro frontend navigation
- Add authentication and state management
- Set up CI/CD pipelines for independent deployments
