# Development Guide

## Quick Start

1. **Install all dependencies:**
   ```bash
   npm run install:all
   ```

2. **Start development servers:**
   ```bash
   npm run dev
   ```
   
   This starts both applications:
   - Remote app: http://localhost:5001
   - Host app: http://localhost:5000

## Development Workflow

### Adding New Components to Remote App

1. Create your component in `remote-app/src/components/`
2. Export it in the federation config in `remote-app/vite.config.js`:
   ```javascript
   exposes: {
     './Button': './src/components/Button',
     './Counter': './src/components/Counter',
     './YourNewComponent': './src/components/YourNewComponent', // Add this
   }
   ```

### Consuming Remote Components in Host App

1. Import the component lazily in `host-app/src/App.jsx`:
   ```javascript
   const YourNewComponent = React.lazy(() => import('remoteApp/YourNewComponent'))
   ```

2. Use it with Suspense:
   ```javascript
   <Suspense fallback={<div>Loading...</div>}>
     <YourNewComponent />
   </Suspense>
   ```

## Port Configuration

- **Host App**: 5000
- **Remote App**: 5001

To change ports, update:
1. `package.json` scripts in each app
2. `remotes` configuration in `host-app/vite.config.js`

## Troubleshooting

### Common Issues

1. **"Module not found" errors**
   - Ensure remote app is running on port 5001
   - Check the federation configuration matches

2. **CORS errors**
   - Both apps should run with `--host` flag
   - Check firewall settings

3. **Hot reload not working**
   - Restart both development servers
   - Clear browser cache

### Debug Mode

To debug federation issues:
1. Open browser dev tools
2. Check Network tab for failed module loads
3. Look for federation-related errors in Console

## Building for Production

```bash
# Build both apps
npm run build

# Preview built apps
npm run preview
```

The built files will be in:
- `host-app/dist/`
- `remote-app/dist/`

## Testing

Currently, no tests are configured. To add testing:

1. Install testing dependencies in each app
2. Add test scripts to package.json files
3. Create test files alongside components

## Next Steps

- Add error boundaries
- Implement shared state management
- Add routing between micro frontends
- Set up CI/CD pipelines
